export { default as ar } from "./ar.cjs";
export { default as az } from "./az.cjs";
export { default as be } from "./be.cjs";
export { default as ca } from "./ca.cjs";
export { default as cs } from "./cs.cjs";
export { default as da } from "./da.cjs";
export { default as de } from "./de.cjs";
export { default as en } from "./en.cjs";
export { default as eo } from "./eo.cjs";
export { default as es } from "./es.cjs";
export { default as fa } from "./fa.cjs";
export { default as fi } from "./fi.cjs";
export { default as fr } from "./fr.cjs";
export { default as frCA } from "./fr-CA.cjs";
export { default as he } from "./he.cjs";
export { default as hu } from "./hu.cjs";
export { default as id } from "./id.cjs";
export { default as is } from "./is.cjs";
export { default as it } from "./it.cjs";
export { default as ja } from "./ja.cjs";
export { default as ka } from "./ka.cjs";
export { default as kh } from "./kh.cjs";
export { default as km } from "./km.cjs";
export { default as ko } from "./ko.cjs";
export { default as lt } from "./lt.cjs";
export { default as mk } from "./mk.cjs";
export { default as ms } from "./ms.cjs";
export { default as nl } from "./nl.cjs";
export { default as no } from "./no.cjs";
export { default as ota } from "./ota.cjs";
export { default as ps } from "./ps.cjs";
export { default as pl } from "./pl.cjs";
export { default as pt } from "./pt.cjs";
export { default as ru } from "./ru.cjs";
export { default as sl } from "./sl.cjs";
export { default as sv } from "./sv.cjs";
export { default as ta } from "./ta.cjs";
export { default as th } from "./th.cjs";
export { default as tr } from "./tr.cjs";
export { default as ua } from "./ua.cjs";
export { default as uk } from "./uk.cjs";
export { default as ur } from "./ur.cjs";
export { default as vi } from "./vi.cjs";
export { default as zhCN } from "./zh-CN.cjs";
export { default as zhTW } from "./zh-TW.cjs";
export { default as yo } from "./yo.cjs";
