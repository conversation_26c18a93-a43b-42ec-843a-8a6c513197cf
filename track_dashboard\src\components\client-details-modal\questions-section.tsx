import React, { useState, useMemo } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { Question } from "@/types/tracking";
import { format } from 'date-fns';
import { exportQuestionsToCSV } from "@/lib/export-utils";
import { RiDownloadLine } from 'react-icons/ri';

interface ExtendedQuestion extends Question {
  agentType: string;
  agentDisplayName: string;
  userId: string;
}

interface Agent {
  type: string;
  displayName: string;
}

interface QuestionsSectionProps {
  questions: ExtendedQuestion[];
  agents: Agent[];
  selectedAgent: string;
  onAgentChange: (agent: string) => void;
}

const ITEMS_PER_PAGE = 10;

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'success':
    case 'completed':
    case 'done':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'pending':
    case 'in progress':
    case 'processing':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'error':
    case 'failed':
    case 'failure':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getModeColor = (mode: string) => {
  switch (mode.toLowerCase()) {
    case 'auto':
    case 'automatic':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'manual':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

export const QuestionsSection: React.FC<QuestionsSectionProps> = ({
  questions,
  agents,
  selectedAgent,
  onAgentChange,
}) => {
  const [currentPage, setCurrentPage] = useState(1);

 
  const handleExport = () => {
    const safeQuestions = Array.isArray(questions) ? questions : [];
    const filteredQuestions = selectedAgent === "all"
      ? safeQuestions
      : safeQuestions.filter(q => q?.agentType === selectedAgent);

    const agentLabel = selectedAgent === "all" ? "all-agents" : selectedAgent;
    const filename = `questions-${agentLabel}`;

    exportQuestionsToCSV(filteredQuestions, filename);
  };

 
  const filteredQuestions = useMemo(() => {
    const safeQuestions = Array.isArray(questions) ? questions : [];
    if (selectedAgent === "all") {
      return safeQuestions;
    }

    return safeQuestions.filter(q => q?.agentType === selectedAgent);
  }, [questions, selectedAgent]);

  
  const totalPages = Math.ceil(filteredQuestions.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentQuestions = filteredQuestions.slice(startIndex, endIndex);

 
  React.useEffect(() => {
    setCurrentPage(1);
  }, [selectedAgent]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <ErrorBoundary>
      <div className="space-y-4">
     
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-700">Filter by Agent:</label>
          <Select value={selectedAgent} onValueChange={onAgentChange}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Select agent" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Agents</SelectItem>
              {agents.map(agent => (
                <SelectItem key={agent.type} value={agent.type}>
                  {agent.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-600">
            Showing {startIndex + 1}-{Math.min(endIndex, filteredQuestions.length)} of {filteredQuestions.length} questions
          </div>

        
          <Button
            onClick={handleExport}
            variant="outline"
            size="sm"
            className="flex items-center gap-2 hover:bg-green-50 hover:border-green-300 hover:text-green-700"
            disabled={filteredQuestions.length === 0}
          >
            <RiDownloadLine className="w-4 h-4" />
            Export ({filteredQuestions.length})
          </Button>
        </div>
      </div>

     
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            Questions & Interactions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {currentQuestions.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <div className="mb-2">No questions found for the selected filters.</div>
                <div className="text-sm">
                  {selectedAgent === "all"
                    ? `Total questions available: ${questions.length}`
                    : `Try selecting "All Agents" or a different agent. Total questions: ${questions.length}`
                  }
                </div>
              </div>
            ) : (
              currentQuestions.map((question, index) => (
                <div
                  key={`${question.sessionName}-${index}`}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900 mb-1">
                        {question.question}
                      </p>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <span>User: {question.userId || 'Unknown'}</span>
                        <span>•</span>
                        <span>Session: {question.sessionName || 'Unknown'}</span>
                        <span>•</span>
                        <span>
                          {question.createdAt ?
                            format(new Date(question.createdAt), 'MMM dd, yyyy HH:mm') :
                            'Unknown date'
                          }
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(question.status)}>
                        {question.status}
                      </Badge>
                      <Badge className={getModeColor(question.mode)}>
                        {question.mode}
                      </Badge>
                    </div>
                  </div>

               
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Agent:</span>
                      <p className="text-gray-600">{question.agentDisplayName}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Response Time:</span>
                      <p className="text-gray-600">{(question.responseTime)/1000|| 0}s</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Feedback:</span>
                      <p className="text-gray-600">
                        {question.likeDislike || 'No feedback'}
                      </p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Category:</span>
                      <p className="text-gray-600">
                        {question.feedbackCategory || 'N/A'}
                      </p>
                    </div>
                  </div>

                 
                  {question.userFeedbackComments && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-md">
                      <span className="font-medium text-gray-700 text-sm">Comments:</span>
                      <p className="text-gray-600 text-sm mt-1">
                        {question.userFeedbackComments}
                      </p>
                    </div>
                  )}

                 
                  {question.rewriteResponse && (
                    <div className="mt-2">
                      <Badge variant="outline" className="text-xs">
                        Response Rewritten
                      </Badge>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>

        
          {totalPages > 1 && (
            <div className="mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                  
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                      className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
    </ErrorBoundary>
  );
};
