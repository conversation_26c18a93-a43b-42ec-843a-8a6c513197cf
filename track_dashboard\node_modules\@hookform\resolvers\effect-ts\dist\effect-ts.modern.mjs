import{toNestErrors as e,validateFieldsNatively as r}from"@hookform/resolvers";import{Effect as o}from"effect";import{decodeUnknown as t,ArrayFormatter as s}from"effect/ParseResult";import{appendErrors as a}from"react-hook-form";function i(i,n={errors:"all",onExcessProperty:"ignore"}){return(c,m,l)=>t(i,n)(c).pipe(o.catchAll(e=>o.flip(s.formatIssue(e))),o.mapError(r=>{const o=!l.shouldUseNativeValidation&&"all"===l.criteriaMode,t=r.reduce((e,r)=>{const t=r.path.join(".");if(e[t]||(e[t]={message:r.message,type:r._tag}),o){const s=e[t].types,i=s&&s[String(r._tag)];e[t]=a(t,o,e,r._tag,i?[].concat(i,r.message):r.message)}return e},{});return e(t,l)}),o.tap(()=>o.sync(()=>l.shouldUseNativeValidation&&r({},l))),o.match({onFailure:e=>({errors:e,values:{}}),onSuccess:e=>({errors:{},values:e})}),o.runPromise)}export{i as effectTsResolver};
//# sourceMappingURL=effect-ts.modern.mjs.map
