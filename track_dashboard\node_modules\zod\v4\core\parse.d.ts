import * as core from "./core.js";
import * as errors from "./errors.js";
import type * as schemas from "./schemas.js";
import * as util from "./util.js";
export type $ZodErrorClass = {
    new (issues: errors.$ZodIssue[]): errors.$ZodError;
};
export type $Parse = <T extends schemas.$ZodType>(schema: T, value: unknown, _ctx?: schemas.ParseContext<errors.$ZodIssue>, _params?: {
    callee?: util.AnyFunc;
    Err?: $ZodErrorClass;
}) => core.output<T>;
export declare const _parse: (_Err: $ZodErrorClass) => $Parse;
export declare const parse: $Parse;
export type $ParseAsync = <T extends schemas.$ZodType>(schema: T, value: unknown, _ctx?: schemas.ParseContext<errors.$ZodIssue>, _params?: {
    callee?: util.AnyFunc;
    Err?: $ZodErrorClass;
}) => Promise<core.output<T>>;
export declare const _parseAsync: (_Err: $ZodErrorClass) => $ParseAsync;
export declare const parseAsync: $ParseAsync;
export type $SafeParse = <T extends schemas.$ZodType>(schema: T, value: unknown, _ctx?: schemas.ParseContext<errors.$ZodIssue>) => util.SafeParseResult<core.output<T>>;
export declare const _safeParse: (_Err: $ZodErrorClass) => $SafeParse;
export declare const safeParse: $SafeParse;
export type $SafeParseAsync = <T extends schemas.$ZodType>(schema: T, value: unknown, _ctx?: schemas.ParseContext<errors.$ZodIssue>) => Promise<util.SafeParseResult<core.output<T>>>;
export declare const _safeParseAsync: (_Err: $ZodErrorClass) => $SafeParseAsync;
export declare const safeParseAsync: $SafeParseAsync;
export type $Encode = <T extends schemas.$ZodType>(schema: T, value: core.output<T>, _ctx?: schemas.ParseContext<errors.$ZodIssue>) => core.input<T>;
export declare const _encode: (_Err: $ZodErrorClass) => $Encode;
export declare const encode: $Encode;
export type $Decode = <T extends schemas.$ZodType>(schema: T, value: core.input<T>, _ctx?: schemas.ParseContext<errors.$ZodIssue>) => core.output<T>;
export declare const _decode: (_Err: $ZodErrorClass) => $Decode;
export declare const decode: $Decode;
export type $EncodeAsync = <T extends schemas.$ZodType>(schema: T, value: core.output<T>, _ctx?: schemas.ParseContext<errors.$ZodIssue>) => Promise<core.input<T>>;
export declare const _encodeAsync: (_Err: $ZodErrorClass) => $EncodeAsync;
export declare const encodeAsync: $EncodeAsync;
export type $DecodeAsync = <T extends schemas.$ZodType>(schema: T, value: core.input<T>, _ctx?: schemas.ParseContext<errors.$ZodIssue>) => Promise<core.output<T>>;
export declare const _decodeAsync: (_Err: $ZodErrorClass) => $DecodeAsync;
export declare const decodeAsync: $DecodeAsync;
export type $SafeEncode = <T extends schemas.$ZodType>(schema: T, value: core.output<T>, _ctx?: schemas.ParseContext<errors.$ZodIssue>) => util.SafeParseResult<core.input<T>>;
export declare const _safeEncode: (_Err: $ZodErrorClass) => $SafeEncode;
export declare const safeEncode: $SafeEncode;
export type $SafeDecode = <T extends schemas.$ZodType>(schema: T, value: core.input<T>, _ctx?: schemas.ParseContext<errors.$ZodIssue>) => util.SafeParseResult<core.output<T>>;
export declare const _safeDecode: (_Err: $ZodErrorClass) => $SafeDecode;
export declare const safeDecode: $SafeDecode;
export type $SafeEncodeAsync = <T extends schemas.$ZodType>(schema: T, value: core.output<T>, _ctx?: schemas.ParseContext<errors.$ZodIssue>) => Promise<util.SafeParseResult<core.input<T>>>;
export declare const _safeEncodeAsync: (_Err: $ZodErrorClass) => $SafeEncodeAsync;
export declare const safeEncodeAsync: $SafeEncodeAsync;
export type $SafeDecodeAsync = <T extends schemas.$ZodType>(schema: T, value: core.input<T>, _ctx?: schemas.ParseContext<errors.$ZodIssue>) => Promise<util.SafeParseResult<core.output<T>>>;
export declare const _safeDecodeAsync: (_Err: $ZodErrorClass) => $SafeDecodeAsync;
export declare const safeDecodeAsync: $SafeDecodeAsync;
