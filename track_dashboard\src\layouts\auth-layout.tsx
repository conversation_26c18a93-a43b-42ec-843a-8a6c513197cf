import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { LocalStorageService, Keys } from '../utils/local-storage';

function AuthLayout() {
   const location = useLocation();

   const token = LocalStorageService.getItem(Keys.Token);
   const authUser = LocalStorageService.getItem(Keys.FlableUserDetails);

   const currentPath = location.pathname;

   const isSetPasswordPage = currentPath.includes('/auth/set-password');

   if (token && authUser && !isSetPasswordPage) {
      return <Navigate to={'/'} replace />;
   }

   return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
         <div className="w-full max-w-lg mx-auto py-12 px-6">
            <Outlet />
         </div>
      </div>
   );
}

export default AuthLayout;
