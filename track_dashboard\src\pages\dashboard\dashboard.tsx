import { Box, Heading,Flex,/*useToast*/useColorMode} from '@chakra-ui/react';


import kpiService from '@/api/service/kpi/index';
import { useApiQuery } from '@/hooks/react-query-hooks';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { useAppDispatch } from '../../store/store';
import { useEffect,useState} from 'react';
import DateRangeSelect from './components/date-select';
import Loading from '@/components/loading';
import { useAppSelector } from '../../store/store';
import { resetDateRange } from '../../store/reducer/kpi-reducer';
import ClientTrackingGrid from '@/components/client-tracking-grid';
import { isTrackingData } from '@/types/tracking';

const Dashboard = () => {
   
  const { dateRange } = useAppSelector((state) => state.kpi)

   const [initialLoadStarted, setInitialLoadStarted] = useState(false);

   const {
      data: aggTrackData,
      isFetching: trackingLoading,
      isLoading: trackingDataLoading,
      errorMessage,
      refetch: getInitialKpiData,
   } = useApiQuery({
      queryKey: ['tracking-data'],
      queryFn: () => kpiService.getTrackData(getPayload()),
      getInitialData: () => undefined,
      enabled: false,
   });

      const getPayload = () => {
      return {
         clientId: LocalStorageService.getItem(Keys.ClientId) as string,
         startDate: dateRange.start,
         endDate: dateRange.end,
      };
   };
    useEffect(() => {
      setInitialLoadStarted(true);
      getInitialKpiData().catch(console.log);
     
   }, [dateRange]);
    useEffect(() => {
      return () => {
         dispatch(resetDateRange());
      };
   }, []);
    const { colorMode } = useColorMode();
   const dispatch= useAppDispatch();
 return (
      <>
         {trackingLoading ? (
            <Loading />
         ) : (
            <Box
               
               sx={{
              
                  '&::-webkit-scrollbar': {
                     display: 'none'
                  },
                  '-ms-overflow-style': 'none',
                  'scrollbar-width': 'none'
               }}
            >
               <Flex direction={'column'} >
                  <Flex justifyContent={'space-between'}>
                     <Flex alignItems='center'>
                        <Heading
                           fontSize={24}
                           size={'xl'}
                           fontWeight={600}
                           color={colorMode === 'dark' ? 'white' : 'inherit'}
                        >
                           Dashboard
                        </Heading>
                     </Flex>
                     <DateRangeSelect  />
                  </Flex>
                  <Flex
                     direction={'column'}
                     justifyContent={'flex-start'}
                     gap={5}
                     mt={6}
                     sx={{
                       
                        '&::-webkit-scrollbar': {
                           display: 'none'
                        },
                        '-ms-overflow-style': 'none',
                        'scrollbar-width': 'none'
                     }}
                  >
                     {aggTrackData && isTrackingData(aggTrackData) && (
                        <ClientTrackingGrid trackingData={aggTrackData} />
                     )}
                  </Flex>
               </Flex>
            </Box>
         )}
      </>
   );
};

export default Dashboard;
