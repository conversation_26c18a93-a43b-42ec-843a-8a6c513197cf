import{validateFieldsNatively as r,toNestErrors as e}from"@hookform/resolvers";import s from"ajv";import o from"ajv-errors";import a from"ajv-formats";import{appendErrors as t}from"react-hook-form";const m=(r,e)=>{const s={},o=r=>{"required"===r.keyword&&(r.instancePath+=`/${r.params.missingProperty}`);const o=r.instancePath.substring(1).replace(/\//g,".");if(s[o]||(s[o]={message:r.message,type:r.keyword}),e){const a=s[o].types,m=a&&a[r.keyword];s[o]=t(o,e,s,r.keyword,m?[].concat(m,r.message||""):r.message)}};for(let e=0;e<r.length;e+=1){const s=r[e];"errorMessage"===s.keyword?s.params.errors.forEach(r=>{r.message=s.message,o(r)}):o(s)}return s},n=(t,n,i={})=>async(c,l,d)=>{const g=new s(Object.assign({},{allErrors:!0,validateSchema:!0},n));o(g),a(g);const p=g.compile(Object.assign({$async:i&&"async"===i.mode},t)),f=p(c);return d.shouldUseNativeValidation&&r({},d),f?{values:c,errors:{}}:{values:{},errors:e(m(p.errors,!d.shouldUseNativeValidation&&"all"===d.criteriaMode),d)}};export{n as ajvResolver};
//# sourceMappingURL=ajv.modern.mjs.map
