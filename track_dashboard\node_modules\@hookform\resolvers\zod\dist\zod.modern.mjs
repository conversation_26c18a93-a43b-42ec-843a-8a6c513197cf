import{validateFieldsNatively as e,toNestErrors as r}from"@hookform/resolvers";import{appendErrors as o}from"react-hook-form";import*as s from"zod/v4/core";function t(e,r){const s={};for(;e.length;){const t=e[0],{code:n,message:a,path:i}=t,c=i.join(".");if(!s[c])if("unionErrors"in t){const e=t.unionErrors[0].errors[0];s[c]={message:e.message,type:e.code}}else s[c]={message:a,type:n};if("unionErrors"in t&&t.unionErrors.forEach(r=>r.errors.forEach(r=>e.push(r))),r){const e=s[c].types,a=e&&e[t.code];s[c]=o(c,r,s,n,a?[].concat(a,t.message):t.message)}e.shift()}return s}function n(e,r){const s={};for(;e.length;){const t=e[0],{code:n,message:a,path:i}=t,c=i.join(".");if(!s[c])if("invalid_union"===t.code&&t.errors.length>0){const e=t.errors[0][0];s[c]={message:e.message,type:e.code}}else s[c]={message:a,type:n};if("invalid_union"===t.code&&t.errors.forEach(r=>r.forEach(r=>e.push(r))),r){const e=s[c].types,a=e&&e[t.code];s[c]=o(c,r,s,n,a?[].concat(a,t.message):t.message)}e.shift()}return s}function a(o,a,i={}){if((e=>"_def"in e&&"object"==typeof e._def&&"typeName"in e._def)(o))return async(s,n,c)=>{try{const r=await o["sync"===i.mode?"parse":"parseAsync"](s,a);return c.shouldUseNativeValidation&&e({},c),{errors:{},values:i.raw?Object.assign({},s):r}}catch(e){if((e=>Array.isArray(null==e?void 0:e.issues))(e))return{values:{},errors:r(t(e.errors,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)};throw e}};if((e=>"_zod"in e&&"object"==typeof e._zod)(o))return async(t,c,d)=>{try{const r="sync"===i.mode?s.parse:s.parseAsync,n=await r(o,t,a);return d.shouldUseNativeValidation&&e({},d),{errors:{},values:i.raw?Object.assign({},t):n}}catch(e){if((e=>e instanceof s.$ZodError)(e))return{values:{},errors:r(n(e.issues,!d.shouldUseNativeValidation&&"all"===d.criteriaMode),d)};throw e}};throw new Error("Invalid input: not a Zod schema")}export{a as zodResolver};
//# sourceMappingURL=zod.modern.mjs.map
