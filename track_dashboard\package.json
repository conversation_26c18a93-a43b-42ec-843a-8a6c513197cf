{"name": "track-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "build:dev": "tsc && vite build --mode dev", "build:prod": "tsc && vite build --mode prod", "build:stage": "tsc && vite build --mode stage", "format": "prettier .", "format:check": "npm run format -- --check", "format:fix": "npm run format -- --write", "lint": "eslint .", "lint:fix": "npm run lint -- --fix", "preview": "vite preview", "serve": "serve -s dist", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["npm run format:fix", "npm run lint:fix"], "*.{json,css,scss}": ["npm run format:fix"]}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@hookform/resolvers": "^5.2.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.2.6", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-query": "^5.40.1", "@types/react-redux": "^7.1.33", "@types/react-router-dom": "^5.3.3", "axios": "^1.6.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.1.7", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "^18.2.0", "react-day-picker": "^9.11.0", "react-dom": "^18.2.0", "react-hook-form": "^7.63.0", "react-icons": "^5.1.0", "react-redux": "^9.1.2", "react-router-dom": "^6.23.0", "react-spinners": "^0.13.8", "recharts": "^3.2.1", "sonner": "^2.0.7", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "tailwindcss-animate": "^1.0.7", "zod": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.1.1", "@types/node": "^20.17.41", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "globals": "^15.0.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "sass": "^1.75.0", "typescript": "^5.5.3", "typescript-eslint": "^7.7.1", "vite": "^5.3.1", "vite-plugin-prettier": "^0.0.6", "vite-plugin-svgr": "^4.3.0"}}