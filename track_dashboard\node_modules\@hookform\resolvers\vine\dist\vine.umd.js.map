{"version": 3, "file": "vine.umd.js", "sources": ["../src/vine.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { SimpleErrorReporter, VineValidator, errors } from '@vinejs/vine';\nimport {\n  ConstructableSchema,\n  ValidationOptions,\n} from '@vinejs/vine/build/src/types';\nimport {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Resolver,\n  appendErrors,\n} from 'react-hook-form';\n\nfunction parseErrorSchema(\n  vineErrors: SimpleErrorReporter['errors'],\n  validateAllFieldCriteria: boolean,\n) {\n  const schemaErrors: Record<string, FieldError> = {};\n\n  for (; vineErrors.length; ) {\n    const error = vineErrors[0];\n    const path = error.field;\n\n    if (!(path in schemaErrors)) {\n      schemaErrors[path] = { message: error.message, type: error.rule };\n    }\n\n    if (validateAllFieldCriteria) {\n      const { types } = schemaErrors[path];\n      const messages = types && types[error.rule];\n\n      schemaErrors[path] = appendErrors(\n        path,\n        validateAllFieldCriteria,\n        schemaErrors,\n        error.rule,\n        messages ? [...(messages as string[]), error.message] : error.message,\n      ) as FieldError;\n    }\n\n    vineErrors.shift();\n  }\n\n  return schemaErrors;\n}\n\nexport function vineResolver<Input extends FieldValues, Context, Output>(\n  schema: VineValidator<ConstructableSchema<Input, Output, Output>, any>,\n  schemaOptions?: ValidationOptions<any>,\n  resolverOptions?: {\n    mode?: 'async' | 'sync';\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function vineResolver<Input extends FieldValues, Context, Output>(\n  schema: VineValidator<ConstructableSchema<Input, Output, Output>, any>,\n  schemaOptions: ValidationOptions<any> | undefined,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using VineJS schema validation\n * @param {T} schema - The VineJS schema to validate against\n * @param {ValidationOptions<any>} [schemaOptions] - Optional VineJS validation options\n * @param {Object} [resolverOptions] - Optional resolver configuration\n * @param {boolean} [resolverOptions.raw=false] - If true, returns raw values instead of validated results\n * @returns {Resolver<Infer<typeof schema>>} A resolver function compatible with react-hook-form\n * @example\n * const schema = vine.compile(\n *   vine.object({\n *     name: vine.string().minLength(2),\n *     age: vine.number().min(18)\n *   })\n * );\n *\n * useForm({\n *   resolver: vineResolver(schema)\n * });\n */\nexport function vineResolver<Input extends FieldValues, Context, Output>(\n  schema: VineValidator<ConstructableSchema<Input, Output, Output>, any>,\n  schemaOptions?: ValidationOptions<any>,\n  resolverOptions: { raw?: boolean } = {},\n): Resolver<Input, Context, Output | Input> {\n  return async (values, _, options) => {\n    try {\n      const data = await schema.validate(values, schemaOptions);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {} as FieldErrors,\n        values: resolverOptions.raw ? Object.assign({}, values) : data,\n      };\n    } catch (error: any) {\n      if (error instanceof errors.E_VALIDATION_ERROR) {\n        return {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              error.messages,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n      }\n\n      throw error;\n    }\n  };\n}\n"], "names": ["parseErrorSchema", "vineErrors", "validateAllFieldCriteria", "schemaErrors", "length", "error", "path", "field", "message", "type", "rule", "types", "messages", "appendErrors", "concat", "shift", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "Promise", "resolve", "validate", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "errors", "raw", "Object", "assign", "_catch", "E_VALIDATION_ERROR", "toNestErrors", "criteriaMode", "e", "reject"], "mappings": "yaAcA,SAASA,EACPC,EACAC,GAIA,IAFA,IAAMC,EAA2C,CAAA,EAE1CF,EAAWG,QAAU,CAC1B,IAAMC,EAAQJ,EAAW,GACnBK,EAAOD,EAAME,MAMnB,GAJMD,KAAQH,IACZA,EAAaG,GAAQ,CAAEE,QAASH,EAAMG,QAASC,KAAMJ,EAAMK,OAGzDR,EAA0B,CAC5B,IAAQS,EAAUR,EAAaG,GAAvBK,MACFC,EAAWD,GAASA,EAAMN,EAAMK,MAEtCP,EAAaG,GAAQO,eACnBP,EACAJ,EACAC,EACAE,EAAMK,KACNE,EAAQ,GAAAE,OAAQF,GAAuBP,EAAMG,UAAWH,EAAMG,QAElE,CAEAP,EAAWc,OACb,CAEA,OAAOZ,CACT,gBAuCM,SACJa,EACAC,EACAC,GAEA,gBAFAA,IAAAA,EAAqC,CAAA,YAEvBC,EAAQC,EAAGC,OAAWC,OAAAA,QAAAC,gCAC9BD,QAAAC,QACiBP,EAAOQ,SAASL,EAAQF,IAAcQ,KAAnDC,SAAAA,GAIN,OAFAL,EAAQM,2BAA6BC,EAAsBA,uBAAC,GAAIP,GAEzD,CACLQ,OAAQ,CAAiB,EACzBV,OAAQD,EAAgBY,IAAMC,OAAOC,OAAO,GAAIb,GAAUO,EAC1D,4DAT8BO,CAAA,EAUzB5B,SAAAA,GACP,GAAIA,aAAiBwB,EAAMA,OAACK,mBAC1B,MAAO,CACLf,OAAQ,CAAA,EACRU,OAAQM,EAAYA,aAClBnC,EACEK,EAAMO,UACLS,EAAQM,2BACkB,QAAzBN,EAAQe,cAEZf,IAKN,MAAMhB,CACR,GACF,CAAC,MAAAgC,GAAAf,OAAAA,QAAAgB,OAAAD,EACH,CAAA,CAAA"}