import * as core from "../core/index.js";
import { ZodRealError } from "./errors.js";
export const parse = /* @__PURE__ */ core._parse(ZodRealError);
export const parseAsync = /* @__PURE__ */ core._parseAsync(ZodRealError);
export const safeParse = /* @__PURE__ */ core._safeParse(ZodRealError);
export const safeParseAsync = /* @__PURE__ */ core._safeParseAsync(ZodRealError);
// Codec functions
export const encode = /* @__PURE__ */ core._encode(ZodRealError);
export const decode = /* @__PURE__ */ core._decode(ZodRealError);
export const encodeAsync = /* @__PURE__ */ core._encodeAsync(ZodRealError);
export const decodeAsync = /* @__PURE__ */ core._decodeAsync(ZodRealError);
export const safeEncode = /* @__PURE__ */ core._safeEncode(ZodRealError);
export const safeDecode = /* @__PURE__ */ core._safeDecode(ZodRealError);
export const safeEncodeAsync = /* @__PURE__ */ core._safeEncodeAsync(ZodRealError);
export const safeDecodeAsync = /* @__PURE__ */ core._safeDecodeAsync(ZodRealError);
